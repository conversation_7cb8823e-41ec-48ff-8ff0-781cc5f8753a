#!/bin/bash
set -e

# Terraform initialization script for GitLab CI/CD
# This script configures Terraform backend for GitLab state management

ENVIRONMENT=${1:-development}
PROJECT_ID=${CI_PROJECT_ID}
CI_JOB_TOKEN=${CI_JOB_TOKEN}
GITLAB_URL=${CI_SERVER_URL}

if [ -z "$PROJECT_ID" ] || [ -z "$CI_JOB_TOKEN" ] || [ -z "$GITLAB_URL" ]; then
    echo "❌ Error: Required GitLab CI variables are missing"
    echo "   PROJECT_ID: $PROJECT_ID"
    echo "   CI_JOB_TOKEN: [${CI_JOB_TOKEN:+SET}${CI_JOB_TOKEN:-MISSING}]"
    echo "   GITLAB_URL: $GITLAB_URL"
    exit 1
fi

echo "🔧 Initializing Terraform for environment: $ENVIRONMENT"
echo "📍 Project ID: $PROJECT_ID"
echo "🌐 GitLab URL: $GITLAB_URL"

# Navigate to environment directory
cd "terraform/environments/$ENVIRONMENT"

# Initialize Terraform with Git<PERSON>ab backend
terraform init \
    -backend-config="address=${GITLAB_URL}/api/v4/projects/${PROJECT_ID}/terraform/state/${ENVIRONMENT}" \
    -backend-config="lock_address=${GITLAB_URL}/api/v4/projects/${PROJECT_ID}/terraform/state/${ENVIRONMENT}/lock" \
    -backend-config="unlock_address=${GITLAB_URL}/api/v4/projects/${PROJECT_ID}/terraform/state/${ENVIRONMENT}/lock" \
    -backend-config="username=gitlab-ci-token" \
    -backend-config="password=${CI_JOB_TOKEN}" \
    -backend-config="lock_method=POST" \
    -backend-config="unlock_method=DELETE" \
    -backend-config="retry_wait_min=5"

echo "✅ Terraform initialized successfully for $ENVIRONMENT environment"
